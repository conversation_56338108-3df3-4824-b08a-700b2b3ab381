﻿@font-face {
  font-family: 'beba';
  src: url("https://www.hiconcn.com/template/cn/css/fonts/BEBAS.eot");
  src: url("https://www.hiconcn.com/template/cn/css/fonts/BEBAS.eot") format("embedded-opentype"), url("https://www.hiconcn.com/template/cn/css/fonts/BEBAS.svg") format("svg");
  font-display: swap;
}
.footer-main .wxbox{
	float:left;
}
.wel-tip-box .items {
  float: left;
  position: relative;
  font-weight: bold;
}
  .d-f-i{
    position: fixed;
    width: 300px;
    height: 197px;
    overflow: hidden;
    right:0;
    top: 150px;
    z-index: 20;
  }
  .d-f-i a{
    display: block;
  }
.ad-road-wrap .road-ul .word .box .i{
  display: none;
}
.ad-road-wrap .road-ul .word .box .p p{
  position: relative;
  padding-left: 15px;
  box-sizing: border-box;
}
.ad-road-wrap .road-ul .word .box .p p:after {
  position: absolute;
  content: "";
  width:5px;
  height: 5px;
  border-radius: 50%;
  background:#666;
  left:0;
  top:15px;

}
.wel-tip-box .items .itembot .i{
	background:#fff;
}
.wel-tip-box .items .itembot .i:nth-child(2n+1){
	background:#e2e2e2;
}
.course-wrap .course-list li{
	font-size: 16px;
	color:#333333;
	line-height: 25px;
	width: 300px;
	float:left;
	/*padding-top: 11px;*/
	position: relative;
	top: 11px;
	padding-left: 18px;
	padding-top:90px;
	box-sizing: border-box;
}
.course-wrap .course-list li:hover:before{
	background:#0060a3;
	transition:.5s ease-in-out;
}
.course-wrap .course-list li:before{
	height: 105px;
	position: absolute;transition:.5s ease-in-out;
	content:"";
	width: 1px;
	left:0;
	top:0;
	background:#dddddd;
}
.course-wrap .course-list li:hover .g0:after{
	transition:.5s ease-in-out;
	background:#0060a3;

}
.course-wrap .course-list li .g0{
	font-size: 20px;
	font-family: "beba";
	color:#0060a3;
	position: relative;
	margin-bottom: 15px;transition:.5s ease-in-out;

}
.course-wrap .course-list li:hover .g0{
	font-size: 35px;transition:.5s ease-in-out;
}

.course-wrap .course-list li .g0:after{
	position: absolute;
	height: 10px;
	width: 10px;
	border: 2px solid #0060a3;
	content:"";
	left: -23px;
	top:15px;transition:.5s ease-in-out;
	bottom:0;
	margin: auto;
	box-sizing: border-box;
	border-radius: 50%;
}
.course-wrap{
	overflow:hidden;
}
.course-wrap .course-list .prev{
	left: 2.5%;
}
.course-wrap .course-list .next{
	right: 2.5%;
}
.course-wrap .course-list li.odd{
	padding-top: 0;
	top: -30px;
}
.course-wrap .course-list li.odd:before{
	top:30px;
	
	height: 100%;
}
.course-wrap .course-list .ovh{
	overflow:visible;
}
.course-wrap .course-list li .p p{
	position: relative;
	padding-left: 10px;
	box-sizing: border-box;    word-break: break-all;
	line-height: 2;
}
.course-wrap .course-list li .p p:after{
	content:"";
	position: absolute;
	width:5px;
	height: 5px;
	border-radius: 50%;
	background:#0060a3;
    left: 0;
    top: 11px;
}
.course-wrap .course-list .butn{
	margin-top: -16px;
}
.course-wrap .course-list{
	    background: url(../image/bg_12.png) repeat-x left center;
}
.course-wrap .course-list .ovh{
	width: 87%;
	margin:0 auto;
    background: transparent;

}
.course-wrap .course-list .butn{
	z-index: 5;
}
.culture0-wrap  .h1-1{
    font-size: 14px;
    position: relative;
    top: 0;
    left: 0;
    line-height: 1.5;
    padding: 20px 30px;
    background: #e6e6e6;
    color: #999;
    left: 0;
    /*top: 40px;*/
    z-index: 1;
    max-width: 90%;
    box-sizing: border-box;
    border-radius: 10px;
    margin:0 auto;
    display: none;
}
.culture0-wrap .swiper-slide{
	position: static;
}
.culture0-wrap  .h1-1:after {
    position: absolute;
    content: "";
    left: 0;
    right: 0;
    margin:0 auto;
    top: -12px;
    width: 20px;
    height: 12px;
    background: url(https://www.hiconcn.com/template/cn/img/lk.png) no-repeat center;
    background-size: 100%;}
.ab-honor-wrap .items .right-words .ul .li{
	position: relative;
	overflow:visible;
}
.ab-honor-wrap .items .right-words .ul .li .li-top{
}
.ab-honor-wrap .items .right-words .ul .li .li-bot{
	font-size: 14px;
	position: absolute;
	top:0;
	left:0;
	line-height: 1.5;
	padding:20px 30px;
	background:#e6e6e6;
	color:#999;
	left:0;
	top: 40px;
	z-index: 1;
	max-width: 300px;
	box-sizing: border-box;
	border-radius: 10px;
	display: none;
}
.ab-honor-wrap .items .right-words .ul .li .li-bot:after{
	position: absolute;
	content:"";
	left: 50px;
	top: -12px;
	width: 20px;
	height: 12px;
	background:url(https://www.hiconcn.com/template/cn/img/lk.png) no-repeat center;
	background-size: 100%;
}
.ab-honor-wrap .items .right-words .ul .li:hover .li-bot{
    display: block;
}
.dj-tit-tip .fixed.titt-tip{
  position: fixed;
  width: 100%;
  top:0;
  left:0;
  z-index: 10;
  background:#ffffff;
}
.h75{
  height: 75px;
}
.ce {
  text-align: center;
}
.ts.fixed{
  height: 100px;
}

.t40 {
  font-size: 40px;
}
.pd0{
  padding:0!important;
}

.w1500 {
  width: 1500px;
  margin: 0 auto;
}
.footer-main .left-wrap .tx1{
  max-width: 850px;
}
.dj-news-0{
  padding:95px 0;
  box-sizing: border-box;
  background:#eee;
}
.dj-news-0 .news-infor .wrap2 .box a{
  width: 100%;
  box-sizing: border-box;

}
.dj-news-0 .news-infor .wrap2 .box{
  width: 19%;
  margin-left: 1.25%;
  margin-bottom: 1.25%;
}
.dj-news-0 .news-infor .wrap2 .box:nth-child(5n+1){
  margin-left:0;
}
.news-infor .wrap2 .box .word{
  font-size: 14px;
  color:#616161;
  line-height: 25px;
  height: 75px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp:3;
          overflow: hidden;
}
.wel-tip-box .wel-tip.fixed{
  position: fixed;
  bottom:0;
  left:0;
  bottom:0;
  width: 100%;
  background:#ffffff;
}
.news-infor .wrap2 .box .more0{
  width: 95px;
  height: 12px;
  background:url(https://www.hiconcn.com/template/cn/img/img2/dm0.png) no-repeat center;
  background-size: 100%;
  margin-top: 20px;
  transition: all 0.3s ease 0s;
}
.news-infor .wrap2 .box .more0{
  background:url(https://www.hiconcn.com/template/cn/img/img2/dm1.png) no-repeat center;transition: all 0.3s ease 0s;
  background-size: 100%;
}
.w1680 {
  width: 1680px;
  margin: 0 auto;
}

.c-tit {
  color: #222222;
  line-height: 1;
}

.t30 {
  font-size: 30px;
}

.y16 {
  font-size: 16px;
}

.y24 {
  font-size: 24px;
}

.kwords {
  font-size: 18px;
  line-height: 30px;
  color: #616161;
}

.wel-con-bot .con-box {
  height: 100%;
}
.wel-con-bot .con-box .images {
  height: 100%;
}
.wel-con-bot .ybox {
  display: inline-block;
  vertical-align: middle;
}
.wel-con-bot .y0, .wel-con-bot .y1 {
  color: #fff;
  line-height: 1;
}
.wel-con-bot .y1 {
  margin-top: 10px;
}
.wel-con-bot .y0 {
  color: #aed2fb;
  font-weight: bold;
}
.dj-news {
  padding-top: 80px;
  padding-bottom: 90px;
  box-sizing: border-box;
}
.dj-news .images-box {
  width: 828px;
  height: 575px;
  overflow: hidden;
}
.dj-news .words-box {
  width: 790px;
}
.dj-news .words-box .items {
  padding-top: 40px;
  display: block;
  padding-bottom: 18px;
  border-top: 1px solid #eeeeee;
  box-sizing: border-box;
}
.dj-news .words-box .items:nth-child(1) {
  border-top: none;
  padding-top: 20px;
}
.dj-news .words-box .items .h0 {
  font-size: 24px;
  color: #212121;
  line-height: 30px;
  height: 30px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  transition: .5s ease-in-out;
}
.dj-news .images-box a{
  overflow:hidden;
}
.dj-news .images-box a img{
  transition: .5s ease-in-out;
}
.dj-news .images-box a:hover img{
  transition: .5s ease-in-out;
  transform:scale3d(1.05,1.05,1);
}
.dj-news .words-box .items:hover .h0{
  transition: .5s ease-in-out;
  color: #f34b1b;
}
.dj-news .words-box .items .time {
  font-size: 16px;
  color: #b6b6b6;
  line-height: 1.2;
  margin-top: 10px;
  margin-bottom: 16px;
}
.dj-news .words-box .items .p {
  font-size: 16px;
  color: #616161;
  line-height: 30px;
  height: 60px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.culture0-wrap {
  padding-top: 95px;
  padding-bottom: 55px;
}
.culture0-wrap .culture0-ul {
  position: relative;
  margin-top: 55px;
}
.culture0-wrap .culture0-line {
  position: absolute;
  width: 100%;
  height: 60px;
  border-bottom: 1px dashed #9c9c9c;
}
.culture0-wrap .items {
  cursor: pointer;
}
.culture0-wrap .images:hover {
  background: #0060a3;
  transition:.5s ease-in-out;
}
.culture0-wrap .images:hover .i1 {
  opacity: 1;
}
.culture2-wrap .culture2-ul .items .images{
  overflow: hidden;
}
.culture2-wrap .culture2-ul .items .images img{
  transition: .5s ease-in-out;
  display: block;
}
.culture2-wrap .culture2-ul .items:hover .images img{
  transform:scale3d(1.05,1.05,1);
  transition: .5s ease-in-out;
}
.culture1-wrap .culture1-ul .images{
  overflow:hidden;
}
.culture1-wrap .culture1-ul .images img{
  transition: .5s ease-in-out;
}
.culture1-wrap .culture1-ul .items:hover .images img{
  transition:.5s ease-in-out;
  transform:scale3d(1.05,1.05,1);
}
.culture0-wrap .images:hover .i0 {
  opacity: 0;
}
.culture0-wrap .images:hover img {
  transition: .5s ease-in-out;
}
.culture0-wrap .images {
  width: 120px;
  height: 120px;
  transition:.5s ease-in-out;
  position: relative;
  background: #f7f7f7;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}
.culture0-wrap .images img {
  transition: .5s ease-in-out;
}
.culture0-wrap .images .i1 {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
}
.culture0-wrap .words {
  text-align: center;
  margin-top: 38px;
}
.culture0-wrap .words .h0 {
  color: #333333;
  font-size: 20px;
  line-height: 24px;
  height: 24px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
.culture0-wrap .words .h1 {
  font-size: 15px;
  line-height: 20px;
  height: 40px;
  margin-top: 10px;
  color: #999999;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.culture1-wrap {
  padding-top: 95px;
  padding-bottom: 140px;
}
.culture1-wrap .culture1-ul {
  margin-top: 80px;
}
.culture1-wrap .culture1-ul img {
  width: 100%;
  display: block;
}
.culture1-wrap .culture1-ul .words, .culture1-wrap .culture1-ul .images {
  width: 50%;
}
.culture1-wrap .culture1-ul .items {
  position: relative;
}
.culture1-wrap .culture1-ul .words {
  position: absolute;
  height: 100%;
  font-size: 16px;
  color: #666666;
  line-height: 30px;
  padding: 70px;
  box-sizing: border-box;
  background: #ffffff;
}
.culture1-wrap .culture1-ul .words .t0 {
  font-size: 36px;
  color: #0060a3;
  line-height: 40px;
  height: 40px;
  overflow: hidden;
  margin-bottom: 45px;
}
.culture1-wrap .culture1-ul .words .wbox {
  height: 100%;
}
.culture1-wrap .culture1-ul .words .wul .i {
  float: left;
  margin: 0 25px;
  font-size: 16px;
}
.culture1-wrap .culture1-ul .words .wul .i span {
  font-weight: bold;
  color: #0060a3;
}
.culture1-wrap .culture1-ul .words .wul .i:nth-child(1) {
  margin-left: 0;
}
.culture1-wrap .culture1-ul .words .p {
  height: calc(100% - 115px);
}
.culture1-wrap .culture1-ul .words.fr {
  right: 0;
  top: 0;
}
.culture1-wrap .culture1-ul .words.fl {
  left: 0;
  top: 0;
}

.culture2-wrap {
  padding-top: 100px;
  padding-bottom: 110px;
  box-sizing: border-box;
}
.culture2-wrap .culture2-ul {
  margin-top: 80px;
}
.culture2-wrap .culture2-ul .items {
  width: 820px;
  float: left;
  margin-left: 40px;
  position: relative;
}
.culture2-wrap .culture2-ul .items:nth-child(2n+1) {
  margin-left: 0;
}
.culture2-wrap .culture2-ul .items .words {
  position: absolute;
  width: 70%;
  bottom: 0;
  left: 0;
  font-size: 16px;
  color: #ffffff;
  padding: 55px 70px;
  box-sizing: border-box;
  height: 100%;
  overflow-y:auto;
}

.culture2-wrap .culture2-ul .items .words::-webkit-scrollbar {
  width: 4px;
  height: 1px;
}
.culture2-wrap .culture2-ul .items .words::-webkit-scrollbar-thumb {
  border-radius: 4px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #535353;
}
.culture2-wrap .culture2-ul .items .words::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  background: #EDEDED;
}



.culture2-wrap .culture2-ul .items .words .t1 {
  line-height: 30px;
}
.culture2-wrap .culture2-ul .items .words .t0 {
  line-height: 1;
  margin-bottom: 30px;
  font-size: 30px;
}

.dj1-wrap .left-words {
  width: 30%;
}
.dj1-wrap .iio {
  min-height: 250px;
}
.dj1-wrap .right-words {
  width: 70%;
  border-left: 1px solid #eeeeee;
  box-sizing: border-box;
}
.dj1-wrap .right-words .pxc {
  line-height: 50px;
  position: relative;
}
.dj1-wrap .right-words .pxc .pli {
  position: relative;
  padding-left: 60px;
  box-sizing: border-box;
}
.dj1-wrap .right-words .pxc .pli:after {
  position: absolute;
  width: 28px;
  height: 28px;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  content: "";
  background: url(https://www.hiconcn.com/template/cn/img/img2/k10.png) no-repeat center;
  background-size: 100%;
}

.profile-wrap .cont{
  width: 100%;
  margin-top: 65px;
  font-size: 16px;
  color:#333333;
  line-height: 2.3;
}
.profile-wrap .cont .right-words{
}
.profile-wrap .cont .right-words img{
    width: auto; float: left;
    margin: 0 50px 50px 0;
}
.wel-tip-box .items .itembot {
  position: absolute;
  bottom: 100px;
  left: 0;
  width: 100%;
  display: none;
}
.wel-tip-box{
  height: 100px;
}
.wel-tip-box .items .itembot .i {
  width: 100%;
  display: block;
  overflow: hidden;
}
.wel-tip-box .items .itembot .i:hover img {
  transition: .5s ease-in-out;
  transform: scale3d(1.05, 1.05, 1);
}
.wel-tip-box .items .itembot .i{
  text-align: center;
  width: 540px;
  margin:0 auto;
  height: 100px;
}
.wel-tip-box .items .itembot img {
  transition: .5s ease-in-out;
  height: 100%;
  display: inline-block;
}
.profile0-about-wrap .page{
  padding-bottom: 0;

}
.wel-tip-box .items:nth-child(1) .itemtop:after {
  display: none;
}
.wel-tip-box .items .itemtop {
  line-height: 100px;
  font-size: 20px;
  color: #222222;
  text-align: center;
  position: relative;
  cursor: pointer;
}
.wel-tip-box .items .itemtop:after {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  content: "";
  height: 60%;
  width: 1px;
  background: #cccccc;
  margin: auto;
}
.ab-honor-wrap .items .left-imaegs img{
  display: block;
}
.wel-tip-box .items .itemtop img {
  display: inline-block;
  vertical-align: middle;
  width: 25px;
  height: 25px;
}
.wel-tip-box .i3 .items {
  width: 33.33%;
}
.wel-tip-box .i2 .items {
  width: 50%;
}
.wel-tip-box .i4 .items {
  width: 25%;
}

.t40 {
  font-size: 40px;
}

.f7 {
  background: #f7f7f7;
}

.wel-news-box {
  padding-bottom: 80px;
  padding-top: 120px;
  box-sizing: border-box;
}
.wel-news-box .swiper-pagination {
  position: static;
  display: inline-block;
  float: none;
  vertical-align: middle;
  width: auto;
  font-size: 16px;
  color: #222222;
  font-weight: bold;
}
.wel-news-box .titbox {
  line-height: 55px;
  height: 55px;
  overflow: hidden;
  margin-bottom: 55px;
}
.wel-news-box .titbox .more {
  margin-left: 35px;
  font-size: 16px;
  color: #222222;
  transition: .5s ease-in-out;
}
.wel-news-box .titbox .more:hover {
  color: #0060a3;
  transition: .5s ease-in-out;
}
.wel-news-box .titbox .swiper-button-prev, .wel-news-box .titbox .swiper-button-next {
  position: static;
  display: inline-block;
  float: none;
  vertical-align: middle;
  margin: 0;
  width: 20px;
  height: 20px;
  background-size: 100%;
  transition: .5s ease-in-out;
}
.wel-news-box .titbox .swiper-button-prev:hover, .wel-news-box .titbox .swiper-button-next:hover {
  transition: .5s ease-in-out;
}
.wel-news-box .titbox .swiper-button-prev {
  background: url(https://www.hiconcn.com/template/cn/img/img2/i6.png) no-repeat center;
  background-size: 100%;
}
.wel-news-box .titbox .swiper-button-prev:hover {
  background: url(https://www.hiconcn.com/template/cn/img/img2/i7.png) no-repeat center;
  background-size: 100%;
}
.wel-news-box .titbox .swiper-button-next {
  background: url(https://www.hiconcn.com/template/cn/img/img2/i8.png) no-repeat center;
  background-size: 100%;
}
.wel-news-box .titbox .swiper-button-next:hover {
  background: url(https://www.hiconcn.com/template/cn/img/img2/i9.png) no-repeat center;
  background-size: 100%;
}
.wel-news-box .item {
  margin-bottom: 10px;
  float: left;
  padding: 30px 0;
  padding-right: 75px;
  box-sizing: border-box;
}
.wel-news-box .item:hover {
  background: #0060a3;
  transition: .5s ease-in-out;
  color: #ffffff;
}
.wel-news-box .item:hover .left {
  transition: .5s ease-in-out;
  color: #ffffff;
}
.wel-news-box .item:hover .right .h0 {
  transition: .5s ease-in-out;
  color: #ffffff;
}
.wel-news-box .item:hover .right .p {
  transition: .5s ease-in-out;
  color: #def1ff;
}
.wel-news-box .item .left {
  width: 145px;
  text-align: center;
  line-height: 1;
  color: #222222;
  transition: .5s ease-in-out;
  font-family: "beba";
}
.wel-news-box .item .left .num {
  font-size: 50px;
  line-height: 1;
  padding-bottom: 10px;
  margin-bottom: 10px;
  position: relative;
}
.wel-news-box .item .left .num:after {
  content: "";
  position: absolute;
  height: 1px;
  width: 45px;
  background: #dddddd;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
}
.wel-news-box .item .left .time {
  font-size: 16px;
}
.wel-news-box .item .right {
  width: calc(100% - 145px);
}
.wel-news-box .item .right .h0 {
  font-size: 20px;
  color: #222222;
  line-height: 22px;
  height: 22px;
  overflow: hidden;
  transition: .5s ease-in-out;
  margin-bottom: 15px;
}
.wel-news-box .item .right .p {
  transition: .5s ease-in-out;
  font-size: 14px;
  color: #666666;
  line-height: 26px;
}

.wel-con-bot {
  display: block;
  height: 140px;
  line-height: 140px;
  color: #ffffff;
  font-size: 15px;
}
.wel-con-bot .words {
  color: #ffffff;
  font-size: 15px;
}

.wel-news-box .item .right .p {
  height: 52px;
  overflow: hidden;
}

.wel-tip-box .items .itemtop br {
  display: none;
}
.header .s-nav a{
  font-size: 14px;
}
.f-box{
  display: inline-block;
  position: relative;
}
.header .s-nav{
  text-align: right;
}
.f-bot{
  position: absolute;
  width: 430px;
  height: 190px;
  border:1px solid #eeeeee;
  box-sizing: border-box;
  background:#fff;
    left: -200px;
    top: 45px;
    text-align: left;
    padding:20px 45px 30px 30px;
    box-sizing: border-box;
    display: none;

}
.f-top{
  cursor: pointer;
}
.f-bot:before{
  content:"";
  width: 22px;
  height: 12px;
  background:url(https://www.hiconcn.com/template/cn/img/img2/i0.png) no-repeat center;
  background-size: 100%;
  top: -12px;
  left:0;
  right:0;
  margin: auto;
  position: absolute;
}
.f-bot .t0{
  line-height: 1.2;
  font-size: 22px;
  color:#222222;
  margin-bottom: 15px;
  margin-top: 8px;
}
.f-bot .t1{
  font-size: 14px;
  color:#666666;
  line-height: 1.4;
}
.header .right0{
  width: 140px;
}
.header .right0 a{
  display: block;
  float:none;
  line-height: 45px;
  font-size: 14px;
  color:#222222;
  transition:.5s ease;
  border-bottom: 1px solid #cccccc;
}
.header .right0 a:hover,.header .right0 a.active{
  transition:.5s ease;
  color:#0072c2;
}
.header .left0{
  width: 165px;
} 

.footer-main .left-wrap .tx0{
  line-height: 1.2;
  font-size: 18px;
  color:#222222;
}
.footer-main .left-wrap .tx1{
  font-size: 14px;
  color:#666666;
  margin-top: 22px;
}
.footer-main .left-wrap .tx1 .i{
  float:left;
  width: 420px;
  line-height: 30px;
}
.wel-con-bot img{
  display: inline-block;
  vertical-align: middle;
  max-width: 250px;
  max-height: 75px;
}
.wel-tip-box{
  position: relative;
  z-index: 10;
}
.characteristics-wrap .items{
  position: relative;
}
.characteristics-wrap .items:before{
  width: 4px;
  height: 80px;
  position: absolute;
  background:#0065b3;
  content:"";
  top:50px;
  left:0;
}
.characteristics-wrap .items:nth-child(3n+1):before{
  display: none;
}
.wel-tip-box .items{
  background:#ffffff;
}
.down-box .items {
  display: block;
  float: left;
  width: 31%;
  height: 240px;
  margin-left: 3.5%;
  margin-bottom: 80px;
  box-sizing: border-box;
}
.down-box .items:nth-child(3n+1) {
  margin-left: 0;
}
.down-box .items:hover img {
  transition: .5s ease-in-out;
  transform: scale3d(1.05, 1.05, 1);
}
.down-box .items .images {
  width: 170px;
  height: 240px;
  border:1px solid #cbcbcb;
  box-sizing: border-box;
  overflow: hidden;
}
.down-box .items .images img {
  transition: .5s ease-in-out;
  display: block;
}
.down-box .items .words {
  height: 240px;
  width: calc(100% - 170px);
  padding:25px 0;
  padding-left: 35px;
  box-sizing: border-box;
}
.down-box .items .words .t0 {
  color: #333333;
  font-size: 20px;
  line-height: 26px;
  height: 26px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  margin-bottom: 20px;
}
.down-box .items .words .more {
  font-size: 15px;
  color: #0060a3;
  margin-top: 50px;
  line-height: 20px;
  height: 20px;
  padding-left: 20px;
  box-sizing: border-box;
  position: relative;
}
.down-box .items .words .more:after {
  position: absolute;
  width: 15px;
  height: 15px;
  content: "";
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  background: url(https://www.hiconcn.com/template/cn/img/img2/i11.png) no-repeat center;
  background-size: 100%;
}
.down-box .items .words .t1 {
  font-size: 15px;
  color: #666666;
  line-height: 25px;
  height: 75px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
img{
  max-width: 100%;
}
.profile-wrap{
  padding-top: 115px; 
     padding-bottom: 135px; 
    transition: normal;
}
.profile-wrap .f48 {
    font-size: 50px;
    color: #0060a3;
    line-height: 50px;
    margin-bottom: 10px;
}
.ab-honor-wrap {
  margin-top: 70px;
}
.ab-honor-wrap .grid-box .column{
	width:19%;
	margin-right:1.25%;
	margin-bottom:1.25%;
}
.ab-honor-wrap .grid-box .column:nth-child(5n){
	margin-right:0;
}
.ab-honor-wrap .grid-box .column .inner-box .txt-center{
	text-align:center;
	line-height:35px;
}
.ab-honor-wrap .grid-box .column .inner-box .txt-center a{
	font-size:20px;
}
.ab-honor-wrap .items {
  position: relative;
  height: 600px;
  background: #f7f7f7;
  font-size: 16px;
  color: #333333;
  margin-bottom: 25px;
}
.ab-honor-wrap .items .left-imaegs {
  width: 560px;
}
.ab-honor-wrap .items .right-words::-webkit-scrollbar {
  width: 4px;
  height: 1px;
}
.ab-honor-wrap .items .right-words::-webkit-scrollbar-thumb {
  border-radius: 4px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #535353;
}
.ab-honor-wrap .items .right-words::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  background: #EDEDED;
}
.ab-honor-wrap .items .right-words {
  position: absolute;
  height: 100%;
  overflow-y: auto;
  width: calc(100% - 560px);
}
.ab-honor-wrap .items .right-words .ul {
  padding: 55px 70px;
  box-sizing: border-box;
}
.ab-honor-wrap .items .right-words .ul:nth-child(n+2) {
  border-top: 1px solid #dddddd;
  box-sizing: border-box;
}
.ab-honor-wrap .items .right-words.fr{
  right:0;
  top:0;
}
.ab-honor-wrap .items .right-words.fl{
  left:0;
  top:0;
}
.ab-honor-wrap .items .right-words .ul .ul-top {
  font-size: 20px;
  font-weight: bold;
  line-height: 22px;
  height: 22px;
  margin-bottom: 15px;
}
.ab-honor-wrap .items .right-words .ul .ul-top img,
.ab-honor-wrap .items .right-words .ul .ul-top .span {
  display: inline-block;
  vertical-align: middle;
    width: auto;
    font-size: 20px;
    color: #000;
}
.ab-honor-wrap .items .right-words .ul .li {
  float: left;
  width: 33.33%;
  line-height: 50px;
  /*overflow: hidden;*/
}
.ab-honor-wrap .items .right-words .ul .li .p{
	display: inline-block;
	line-height: 1.2;
	vertical-align: top;
	width: calc(100% - 20px);
}
.ab-honor-wrap .items .right-words .ul .li span {
  color: #0a66a6;
  font-weight: bold;
  display: inline-block;
  line-height: 1.2;
  vertical-align: top;
}
.ab-honor-wrap .items .right-words .ul .li.active {
  width: 100%;
}
/**/
.w1340 {
  width: 1340px;
  margin: 0 auto;
}

.ad-road-wrap .road-tit {margin:0 auto;
  text-align: center;
  margin-top: 50px;
  margin-bottom: 45px;
  width: 600px;

}
.ad-road-wrap .road-tit .span {
  display: inline-block;
  font-size: 16px;
  color: #999999;
  transition: .5s ease-in-out;
  line-height: 1.2;
  cursor: pointer;
}
.ad-road-wrap .road-tit .swiper-slide{
	margin:0 auto;
}
.ad-road-wrap .road-tit .span:hover, .ad-road-wrap .road-tit .span.active {
  color: #0060a3;
  transition: .5s ease-in-out;
}
.ad-road-wrap .road-ul {
  position: relative;
  padding-top: 68px;
  box-sizing: border-box;
}
.ad-road-wrap .road-ul:after {
  position: absolute;
  width: 18px;
  height: 18px;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  background: url(https://www.hiconcn.com/template/cn/img/img2/i14.png) no-repeat center;
  background-size: 100%;
}
.ad-road-wrap .road-ul:before {
  height: 50px;
  width: 2px;
  background: #dddddd;
  content: "";
  left: 0;
  top: 18px;
  right: 0;
  margin: auto;
  position: absolute;
}
.ad-road-wrap .road-ul .items {
  position: relative;
  min-height: 300px;
}
.ad-road-wrap .road-ul .items:before {
  height: 100%;
  width: 2px;
  background: #dddddd;
  content: "";
  left: 0;
  top: 0;
  right: 0;
  margin: auto;
  position: absolute;
}
.ad-road-wrap .road-ul .items:nth-child(n+2) .words {
  margin-top: -230px;
}
.ad-road-wrap .road-ul .words {
  width: 50%;
  position: relative;
  padding-bottom: 10px;
}
.ad-road-wrap .road-ul .words:before {
  position: absolute;
  content: "";
  height: 2px;
  width: 120px;
  top: 11px;
  background: #dddddd;
}
body{
  /*overflow:hidden;*/
}
.ad-road-wrap .road-ul .words.fl .g0 {
  text-align: right;
}
.ad-road-wrap .road-ul .words.fl:before {
  right: 0;
}
.ad-road-wrap .road-ul .words.fr .g0 {
  text-align: left;
}
.ad-road-wrap .road-ul .words.fr:before {
  left: 0;
}
.ad-road-wrap .road-ul .word {
  width: 510px;
}
.ad-road-wrap .road-ul .word .g0 {
  font-size: 20px;
  line-height: 25px;
  height: 25px;
  color: #222222;
  font-family: Arial;
  margin-bottom: 30px;
}
.ad-road-wrap .road-ul .word .box {
  background: #f7f7f7;
}
.ad-road-wrap .road-ul .word .box .h0 {
  line-height: 60px;
  height: 60px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  background: #0060a3;
  color: #ffffff;
  font-size: 16px;
  padding: 0 35px;
  box-sizing: border-box;
}
.ad-road-wrap .road-ul .word .box .img {
  padding: 35px;
  box-sizing: border-box;
  font-size: 16px;
  color: #333333;
  line-height: 30px;
}
.ad-road-wrap .road-ul .word .box .img:hover img {
  transition: .5s ease-in-out;
  transform: scale3d(1.05, 1.05, 1);
}
.ad-road-wrap .road-ul .word .box .i {
  overflow: hidden;
}
.ad-road-wrap .road-ul .word .box .i img {
  transition: .5s ease-in-out;
  width: 100%;
}
.ad-road-wrap .road-ul .word .box .p {
  margin-top: 35px;
}
.ce{
  text-align: center;
}

.culture1-wrap .culture1-ul .words .p{
  overflow-y:auto;
}
.dj-tit-tip{
  height: 106px;
}

.pword::-webkit-scrollbar {
  width: 4px;
  height: 1px;
  /*opacity: 0;*/
}
.pword::-webkit-scrollbar-thumb {
  border-radius: 4px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #535353;
  /*opacity: 0;*/

}
.pword::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  background: #EDEDED;
  /*opacity: 0;*/

}

.images img{
  display: inline-block;
}

/*2*/
.guidance-top {
  padding-top: 100px;
  box-sizing: border-box;
}
.guidance-top .guidance-tit {
  font-size: 30px;
  line-height: 1;
  margin-bottom: 40px;
}
.guidance-top img {
  max-width: 100%;
}
.guidance-top .guidance-list {
  padding: 60px 70px;
  box-sizing: border-box;
  background: #006cb8;
}
.guidance-top .guidance-list .imgbox {
  background: #ffffff;
}
.guidance-top .guidance-list .imgbox img {
  display: block;
}
.guidance-top .guidance-list .imgbot .li {
  width: 13%;
  margin-left: 4.4%;
  float: left;
}
.guidance-top .guidance-list .imgbot .li:nth-child(1) {
  margin-left: 0;
}

.guidance-bot {
  padding-bottom: 80px;
  margin-top: 40px;
  box-sizing: border-box;
}
.guidance-bot .tit {
  font-size: 18px;
  color: #222222;
  background: #dddddd;
  text-align: center;
  height: 80px;
  line-height: 80px;
}
.guidance-bot .guidance0-ul {
  background: #ffffff;
}
.guidance-bot .guidance0-ul .ul:nth-child(n+2) {
  border-top: 1px solid #dddddd;
}
.guidance-bot .guidance0-ul .items {
  width: 25%;
  float: left;
  font-size: 15px;
  color: #999999;
  padding: 40px 0;
  padding-top: 50px;
  padding-left: 50px;
  box-sizing: border-box;
}
.guidance-bot .guidance0-ul .items .t0 {
  font-size: 16px;
  color: #222222;
  line-height: 20px;
  height: 20px;
  position: relative;
}
.guidance-bot .guidance0-ul .items .t0:after {
  content: "";
  width: 5px;
  height: 5px;
  background: #0060a3;
  position: absolute;
  border-radius: 50%;
  left: -16px;
  top: 0;
  bottom: 0;
  margin: auto;
}
.guidance-bot .guidance0-ul .items .t1 {
  line-height: 25px;
  padding-left: 46px;
  box-sizing: border-box;
  position: relative;
  margin-top: 10px;
}
.guidance-bot .guidance0-ul .items .t1 span {
  position: absolute;
  display: block;
  width: 46px;
  left: 0;
  top: 0;
}
.guidance-bot .guidance0-ul .items a {
  color: #006cb8;
  display: block;
}
.guidance-bot .guidance0-ul .items.cur{
  border-bottom: none;
}


.news-infor.news-infor-2 .wrap2 .box{
  position: static!important;
  width: 23%;
  margin-left: 2.66%;
  
}
.news-infor.news-infor-2 .wrap2 .box:nth-child(4n+1){
  margin-left: 0;
}
.news-infor.news-infor-2 .wrap2 .box a{
  width: 100%;
  box-sizing: border-box;
}
.news-infor.news-infor-2 .wrap2 .box{
  margin-bottom: 50px;
  border:1px solid #dddddd;
  background-color:transparent;
  box-sizing: border-box;
}
.news-infor.news-infor-2 .wrap2 .box .f18{
  font-size: 20px;
  color:#222222;
  line-height: 28px;
  height: 56px;

}
.news-infor.news-infor-2 .wrap2 .box .date{
  font-size: 14px;
  font-family: Arial;
  line-height: 1;
  margin-top: 15px;
  margin-bottom: 30px;
}
.news-infor.news-infor-2 .wrap2 .box .word{
  font-size: 15px;
  color:#666666;
  line-height: 28px;
  height: 56px;
  overflow:hidden;
  margin-top: 26px;
}

.dj-tit-tip {
  border-bottom: 1px solid #f0f0f0;
}
.dj-tit-tip .titt-tip {
  height: 105px;
  line-height: 105px;
  font-size: 16px;
  color: #212121;
}
.dj-tit-tip .titt-tip a {
  font-size: 16px;
  color: #212121;
  transition: .5s ease-in-out;
}
.dj-tit-tip .titt-tip a:hover {
  color: #f34b1b;
  transition: .5s ease-in-out;
}
.dj-tit-tip .titt-tip .home-right a:nth-child(n+2) {
  margin-left: 60px;
}
.dj-tit-tip .titt-tip .home-right a {
  display: block;
  float: left;
  position: relative;
}
.dj-tit-tip .titt-tip .home-right a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 3px;
  bottom: -4px;
  left: 0;
  right: 0;
  opacity: 0;
  transition: .5s ease-in-out;
  margin: auto;
  background: #f34b1b;
}
.dj-tit-tip .titt-tip .home-right a.active {
  color: #f34b1b;
}
.dj-tit-tip .titt-tip .home-right a.active:after {
  opacity: 1;
  transition: .5s ease-in-out;
}
.dj-tit-tip .titt-tip .home-left a {
  display: inline-block;
  float: left;
}
.dj-tit-tip .titt-tip .home-left a:hover, .dj-tit-tip .titt-tip .home-left a.active {
  color: #f34b1b;
}
.dj-tit-tip .titt-tip .home-left .i {
  position: relative;
  padding-left: 40px;
  margin-left: 35px;
  box-sizing: border-box;
}
.dj-tit-tip .titt-tip .home-left .i:after {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  background: url(https://www.hiconcn.com/template/cn/img/img2/dj1.png) no-repeat center;
  background-size: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.dj-tit-tip .titt-tip .home-left .home span {
  display: inline-block;
  vertical-align: middle;
  width: 15px;
  height: 15px;
  background: url(https://www.hiconcn.com/template/cn/img/img2/dj0.png) no-repeat center;
  background-size: 100%;
}

.dj1-wrap .right-words .pxc .pli:last-child:after {
  display: none;
}

.dj1-wrap .left-words {
  line-height: 1.2;
  padding-top: 75px;
  box-sizing: border-box;
}
.dj1-wrap .left-words .t0 {
  color: #f34b1b;
}
.dj1-wrap .left-words .t1 {
  color: #bfbfbf;
  font-family: Arial;
  text-transform: uppercase;
  margin-top: 10px;
}

.dj1-wrap .right-words {
  padding: 80px 100px;
  box-sizing: border-box;
}
.dj1-wrap .right-words .pxc {
  padding-left: 100px;
}

.characteristics-wrap .itemsbox:nth-child(n+2) {
  border-top: 1px solid #eeeeee;
}
.characteristics-wrap .items {
  display: inline-block;
  vertical-align: top;
  width: 33.33%;
  padding: 50px 60px;
  border-left: 1px solid #eeeeee;
  box-sizing: border-box;
}
.characteristics-wrap .items:nth-child(1) {
  border-left: none;
}
.characteristics-wrap .items .images {
  overflow: hidden;
}
.characteristics-wrap .items .images img {
  transition: .5s ease-in-out;
}
.characteristics-wrap .items:hover .images img {
  transition: .5s ease-in-out;
  transform: scale3d(1.05, 1.05, 1);
}
.characteristics-wrap .items .words {
  padding-top: 25px;
  box-sizing: border-box;
}
.characteristics-wrap .items .p {
  font-size: 16px;
  line-height: 28px;
  color: #686868;
  height: 224px;
  overflow-y:auto;
}
.characteristics-wrap .items .t0 {
  font-size: 36px;
  color: #f34b1b;
  line-height: 40px;
  height: 40px;
  overflow: hidden;
}
.characteristics-wrap .items .t1 {
  font-size: 20px;
  font-weight: bold;
  line-height: 24px;
  height: 24px;
  color: #686868;
  margin-top: 13px;
  margin-bottom: 25px;
}

.t60 {
  font-size: 60px;
  line-height: 1;
}

.garden {
  padding-top: 43px;
  padding-bottom: 25px;
  box-sizing: border-box;
}
.garden .items {
  position: relative;
  margin-bottom: 20px;
}
.garden .items .words {
  width: 635px;
  position: absolute;
  height: 100%;
  overflow-y: auto;
  background: #ffffff;
  padding: 85px 70px;
  box-sizing: border-box;
}
.garden .items .words.fl {
  left: 0;
  top: 0;
}
.garden .items .words.fr {
  right: 0;
  top: 0;
  text-align: right;
}
.garden .items .words .p {
  font-size: 18px;
  color: #212121;
  line-height: 30px;
  margin-top: 25px;
}
.garden .items .image {
  width: calc(100% - 635px);
}
.garden .items .i {
  overflow: hidden;
}
.garden .items .i:hover imgg {
  transform: scale3d(1.05, 1.05, 1);
}
.garden .items .i img {
  transition: .5s ease-in-out;
}

.garden-swiper0 .swiper-pagination .swiper-pagination-bullet {
  width: 60px;
  height: 4px;
  background: #fefbfe;
  opacity: 1;
  border-radius: 0;
}
.garden-swiper0 .swiper-pagination .swiper-pagination-bullet-active {
  background: #ff3323;
}
.pc0{
  display: block;
}
.m0{
  display: none;
}
.m-ins-navbox10.m-ins-navbox .left .ico{
  background-color: #f34b1b;
}
.m-ins-navbox10.m-ins-navbox .right .tit{
  background: url(https://www.hiconcn.com/template/cn/img/img2/dj2.png) no-repeat 93% center;
  color:#f34b1b;
}
.m-ins-navbox .right .subbox a:hover{
  color:#f34b1b;
}

.dj-news-0 .news-infor .wrap2{
  padding-top:0;
}
.header .right{
  width: auto;
}

.search-box input{
  border:none;
  background:none;
  outline: none;

}
.search-box{
  border-bottom: 1px solid #dddddd;
  position: relative;
}
.search-box input[type = submit]{
    width: 18px;
    height: 17px;
    background: no-repeat center;
    position: absolute;
    top:0;
    bottom:0;
    right: 0;
    margin: auto;
    text-indent: -999px;
    overflow:hidden;
}
.search-box input[type = text]{
  width: 100%;
  height: 100%;
}




.profile0-about-wrap .right-words{
  width: 100%!important;
  float:none;
}
.profile0-about-wrap .left-images {
    width: 45%;
    margin-right: 3%;
    height: auto;
}
@media screen and (max-width: 1850px){
  .profile0-about-wrap .left-images{
    width:45%;
    height: auto;
  }
  .dj-news .images-box{
    width: 50%;
    height: auto;
  }
  .dj-news .words-box{
    width:47%;
  }
  .guidance-top .guidance-list{
    padding:50px 60px;
  }
  .profile0-about-wrap .left-images img{
    width: 100%;
  }
  .profile0-about-wrap .right-words{
    width: 53%;
  }
  .profile-wrap {
      padding-top: 85px;
      padding-bottom: 100px;
  }
  .profile-wrap .cont{
    margin-top:50px;
  }
  .profile-wrap .f48{
    font-size: 45px;
    line-height: 1;
  }
  .profile-wrap .f36{
    font-size: 35px;
    line-height: 1;
  }
  .w1500{
    width: 85%;
  }
  .w1680{
    width: 95%;
  }
  .culture2-wrap .culture2-ul .items{
    width: 49%;
    margin-left: 2%;
  }
  .culture1-wrap .culture1-ul .words{
    padding:60px;
  }
  .t60{
    font-size: 50px;
  }
  .garden .items .words{
    padding: 5% 6%;
  }
  .dj1-wrap .right-words{
    padding: 4.5% 5.5%;
  }
  .dj1-wrap .left-words{
    padding-top: 4%;
  }
  .dj1-wrap .right-words .pxc{
    padding-left: 5%;
  }

}

.industrial-metal .listwrap img.h-f{
	height: auto!important;
}

@media screen and (max-width: 1750px) {
  .wel-news-box {
    padding-top: 60px;
    padding-bottom: 100px;
  }

  .guidance-top .guidance-list{
    padding:40px 50px;
  }
  .c-tit.t40{
    font-size: 35px;
  }
  .culture1-wrap .culture1-ul .words .t0{
    font-size: 28px;
  }

  .culture1-wrap .culture1-ul .words{
    padding:50px;
  }

  .wel-tip-box .items .itembot .i{
    width: 100%;
    display: block;
    height: auto;
    line-height: 1;
  }
  .wel-tip-box .items .itembot img{
    width: 100%;
    height: auto;
    display: block;
  }



  .wel-news-box .item .left .num {
    font-size: 45px;
  }

  .wel-con-bot {
    height: 120px;
    line-height: 120px;
  }

  .wel-news-box .titbox {
    margin-bottom: 40px;
  }

  .wel-news-box .item {
    padding: 25px 0;
    padding-right: 55px;
  }

  .wel-news-box .titbox .t40 {
    font-size: 36px;
  }
  .culture1-wrap {
      padding-top: 75px;
      padding-bottom: 110px;
  }
  .culture1-wrap .culture1-ul{
    margin-top: 55px;
  }
  .culture2-wrap {
      padding-top: 80px;
      padding-bottom: 90px;
  }
  .culture2-wrap .culture2-ul{
    margin-top: 60px;
  }


  .ab-honor-wrap{
    margin-top: 60px;
  }

}
@media screen and (max-width: 1650px){
  .ts.fixed{
    height: 80px;
  }
  .d-f-i{
    width: 200px;
    height: auto;
    top: 150px;
  }



  .down-box .items .images{
    width: 150px;
    height: auto;
  }
  .dj-tit-tip{
    height: 81px;
  }
  .dj-tit-tip .titt-tip{
    line-height: 80px;
    height: 80px;
  }
  .dj-news-0{
    padding:75px 0;
  }
  .characteristics-wrap .items:before{
    top: 35px;
    height: 60px;
    width: 3px;
  }
  .dj-news{
    padding-top: 55px;
    padding-bottom: 70px;
    box-sizing: border-box;
  }
  .dj-news .words-box .items .h0{
    font-size: 20px;
  }
  .dj1-wrap .right-words .pxc .pli{
    padding-left: 50px;
  }
  .dj-news .words-box .items{
    padding-top: 25px;
  }
  .dj-news .words-box .items .time{
    font-size: 14px;
    margin-top: 6px;
    margin-bottom: 15px;
  }
  .kwords{
    font-size: 16px;
    line-height: 28px;
  }
  .guidance-bot .guidance0-ul .items{
    padding:35px 0;
    padding-left: 40px;
  }
  .dj1-wrap .left-words .t0{
    font-size: 34px;
  }
  .dj1-wrap .left-words .t1{
    font-size: 26px;
  }

  .w1340{
    width: 100%;
  }
  .ab-honor-wrap .items{
    height: auto;
  }
  .ab-honor-wrap .items .right-words .ul{
    padding:40px 55px;
  }
  .ab-honor-wrap .items .right-words .ul .ul-top{
    font-size: 18px;
  }
  .ab-honor-wrap .items .right-words .ul .li{
    line-height: 40px;
    font-size: 14px;
    height: 40px;
  }
  .ab-honor-wrap .items .left-imaegs{
    width: 35%;
  }
  .ab-honor-wrap .items .right-words{
    width: 65%;
  }
  .down-box .items{
    height: auto;
  }
  .down-box .items .words{
    height: auto;
    width: calc(100% - 150px);
        padding: 15px 0;
      padding-left: 20px;
  }
  .down-box .items .words .t0{
    margin-bottom: 15px;
  }
  .down-box .items .words .more{
    margin-top: 35px;
  }
  .ad-road-wrap .road-ul .word{
    width: calc(100% - 100px);
  }
  .ad-road-wrap .road-ul .words:before{
    width: 70px;
  }
  .ad-road-wrap .road-ul .word .box .p{
    margin-top: 20px;
  }
  .ad-road-wrap .road-tit{
    margin-top: 40px;
    margin-bottom: 30px;
  }
  .ad-road-wrap .road-ul{
    padding-top: 58px;
  }
  .ad-road-wrap .road-ul:before{
    height: 40px;
  }
  .c-tit-t40{
    font-size: 25px;
  }
  .culture1-wrap .culture1-ul .words .t0{
    font-size: 22px;
  }
  .culture0-wrap {
    padding-top: 75px;
    padding-bottom: 35px;
  }

  .news-infor.news-infor-2 .wrap2 .box .f18{
    font-size: 18px;
    line-height: 26px;
    height: 52px;
  }
  .news-infor.news-infor-2 .wrap2 .box a{
    padding:20px;
  }
  .news-infor.news-infor-2 .wrap2 .box .date{
    margin-top: 12px;
    margin-bottom: 20px;
  }
  .t60{
    font-size: 40px;
  }
  .garden .items .words{
    padding: 3% 4.5%;
    width: 450px;
  }
  .garden .items .image {
      width: calc(100% - 450px);
  }
  .garden .items .words .p{
    font-size: 16px;
    line-height: 26px;
  }
  .characteristics-wrap .items .t0{
    font-size: 32px;
    height: auto;
    line-height: 1.2;
  }
  .characteristics-wrap .items .t1{
    line-height: 1.2;
    font-size: 18px;
    height: auto;
  }
  .characteristics-wrap .items{
    padding:35px 40px;
  }
  .dj-news-0 .news-infor .wrap2 .box{
    width: 32%;
    margin-left: 2%;
  }
  .dj-news-0 .news-infor .wrap2 .box:nth-child(5n+1){
    margin-left: 2%;
  }
  .dj-news-0 .news-infor .wrap2 .box:nth-child(3n+1){
    margin-left: 0;
  }
  .news-infor.news-infor-2 .wrap2 .box{
    margin-bottom: 2%;
  }
    .wel-tip-box{
    height: 80px;
  }
  .wel-tip-box .items .itembot {
    bottom:80px;
  }
    .wel-tip-box .items .itemtop {
    line-height: 80px;
  }
}
@media screen and (max-width: 1450px) {
  .wel-news-box .titbox .t40 {
    font-size: 32px;
  }


  .garden-swiper0 .swiper-pagination .swiper-pagination-bullet{
    width: 40px;
    height: 3px;
  }
  .t60{
    font-size: 32px;
  }
  .dj1-wrap .left-words .t0{
    font-size: 28px;
  }
  .dj1-wrap .left-words .t1{
    font-size: 24px;
  }
  .guidance-top{
    padding-top: 75px;
  }
  .guidance-wrap{
    width: 94%;
    padding:0 3%;
  }
  .guidance-top .guidance-tit{
    font-size: 22px;
    margin-bottom: 30px;
  }
  .guidance-top .guidance-list{
    padding:30px 35px;
  }
  .guidance-bot .guidance0-ul .items{
    font-size: 14px;
  }
  .guidance-bot .guidance0-ul .items .t0:after{
    left: -13px;
  }
  .guidance-bot .guidance0-ul .items .t1 span{
    width: 43px;
  }
  .guidance-bot .guidance0-ul .items .t1{
    padding-left: 44px;
    line-height: 20px;
  }
  .guidance-bot .guidance0-ul .items{
    padding-left: 25px;
  }
  .culture0-wrap .images{
    width: 100px;
    height:  100px;
  }
  .culture0-wrap .words .h0{
    font-size: 18px;
  }
  .culture0-wrap .culture0-line{
    height: 50px;
  }
  .c-tit.t40{
    font-size: 30px;
  }
  .culture2-wrap .culture2-ul .items .words{
    padding:35px 50px;
  }
  .culture2-wrap .culture2-ul .items .words .t1{
    font-size: 14px;
    line-height: 24px;
  }
  .culture2-wrap .culture2-ul .items .words .t0{
    font-size: 20px;
    margin-bottom: 20px;
  }
  .culture1-wrap .culture1-ul .words{
    padding:35px;
  }
  .culture1-wrap .culture1-ul .words .t0{
    font-size: 20px;
    line-height: 32px;
    height: 32px;
    margin-bottom: 25px;
  }
  .culture1-wrap .culture1-ul .words .wul{
    line-height: 20px;
  }
  .culture1-wrap .culture1-ul .words .p{
    height: calc(100% - 77px);
  }
  .culture1-wrap .culture1-ul .words .wul .i{
    margin:0 15px;
    font-size: 14px;
  }
  .culture1-wrap .culture1-ul .words .p{
    font-size: 14px;
    line-height: 25px;
  }
  .ad-road-wrap .road-ul .word .box .img{
    padding:25px;
  }
  .down-box .items .words{
    padding: 10px 0;
        padding-left: 15px;
  }
  .ad-road-wrap .road-ul .word .box .h0{
    height: 50px;
    line-height: 50px;
  }
  .wel-news-box .item .left .num {
    font-size: 40px;
    }
    .profile-wrap {
        padding-top: 60px;
        padding-bottom: 80px;
    }
  .wel-news-box .titbox {
    line-height: 45px;
    height: 45px;
    margin-bottom: 32px;
  }
  .down-box .items .words .t0{
    font-size: 18px;
    line-height: 20px;
    height: 20px;
  }
  .profile-wrap .cont{
    font-size: 14px;
    margin-top: 30px;
  }
  .profile-wrap .f48{
    font-size: 35px;
  }
  
  .profile-wrap .f36{
    font-size: 30px;
  }

  .wel-news-box {
    padding-top: 45px;
    padding-bottom: 80px;
  }

  .wel-news-box .item {
    padding-right: 30px;
  }

  .ab-honor-wrap .items .right-words .ul{
    padding:30px 40px;
  }
  .ab-honor-wrap{
    margin-top: 50px;
  }
  .footer-main{
        padding-top: 45px;
    padding-bottom: 50px;
  }
  .down-box .items{
  	margin-bottom: 50px;
  }
}

@media screen and (max-width: 1350px){
  .down-box .items .images img{
    display: block;
    width: 100%;
  }
  .culture2-wrap .culture2-ul .items .words .t1{
  	line-height: 22px;
  }
  .dj-news-0{
    padding:55px 0;
  }
  .dj-news .words-box .items .p{
    font-size: 15px;
    line-height: 25px;
    height: 50px;
  }
  .characteristics-wrap .items:before{
    top: 20px;
  }

  .dj-news{
    padding-top: 45px;
    padding-bottom: 55px;
  }
  .dj-news .words-box .items{
    padding-top: 15px;
    padding-bottom: 10px;
  }
  .dj-news .words-box .items .h0{
    font-size: 18px;
  }
  .characteristics-wrap .items .t1{
    font-size: 16px;
  }
  .characteristics-wrap .items .words{
    padding-top: 15px;
  }
  .characteristics-wrap .items .p{
    font-size: 14px;
    line-height: 25px;
  }
  .characteristics-wrap .items{
    padding:20px 30px;
  }
  .characteristics-wrap .items .t0{
    font-size: 25px;
  }
  .garden .items .words .p{
    font-size: 14px;
    line-height: 24px;
  }
  .t60{
    font-size: 25px;
  }
  .garden .items .words .p{
    margin-top: 15px;
  }

  .down-box .items .images{
    width: 120px;
  }
  .down-box .items .words{
    width: calc(100% - 120px);
  }
  .ab-honor-wrap .items .right-words .ul .li{
    font-size: 13px;
  }
  .ab-honor-wrap .items .right-words .ul{
    padding:20px 25px;
  }
  .ab-honor-wrap .items .right-words .ul .li{
    /*line-height: 2;*/
  }
  .dj-tit-tip .titt-tip .home-right a:nth-child(n+2){
    margin-left: 45px;
  }
  .dj-tit-tip .titt-tip .home-left .i{
    padding-left: 25px;
    margin-left: 20px;
  }
  .down-box .items .words .t0{
  	font-size: 16px;
  }
}
@media screen and (max-width: 1190px){
  .guidance-bot .guidance0-ul .items{
    width: 100%;
        border-bottom: 1px solid #dddddd;
  }
  .guidance-bot .guidance0-ul .ul:nth-child(n+2){
    border:none;
  }
  .header .nav li{
    margin-right: 20px;
  }
  .down-box .items{
  	width: 49%;
  	margin-left: 2%;
  }
  .down-box .items:nth-child(3n+1){
  	margin-left: 2%;
  }
  .down-box .items:nth-child(2n+1){
  	margin-left: 0;
  }
}
@media screen and (max-width: 1100px){
  .down-box .items .images,.down-box .items .words{
    /*width: 100%;*/
  }
  .down-box .items .words{
    padding:15px;
  }
  .ab-honor-wrap .items .right-words .ul .li{
    width: 50%;
  }
  .ab-honor-wrap .items .right-words .ul .li.active{
    width: 100%;
  }
  .characteristics-wrap .items .t0{
    font-size: 20px;
  }

  .news-infor.news-infor-2 .wrap2 .box{
    width: 32%;
    margin-left: 2%;
  }
  .news-infor.news-infor-2 .wrap2 .box:nth-child(4n+1){
    margin-left: 2%;
  }
  .news-infor.news-infor-2 .wrap2 .box:nth-child(3n+1){
    margin-left: 0;
  }




}
@media screen and (max-width: 1023px) {
  .wel-news-box .titbox .t40 {
    font-size: 28px;
  }
  .profile0-about-wrap .left-images{
  	margin-right:0;
  }
  .ad-road-wrap .road-ul .word .box .p p:after{
    top: 10px;
  }
    .d-f-i{
    width: 150px;
    top: 90px;
  }

  .down-box .items .words .t1{
  	line-height: 20px;
  	height: 60px;
  }
	.culture0-wrap .h1-1{
		display: none!important;
	}
    .header.m-wrap .zihao {
      width: 26px;
      height: 46px;
      background: url(https://www.hiconcn.com/template/cn/img/img2/ziti.png) no-repeat center;
      background-size: 100%;
      margin-right: 10px;
  }
  .header .left{
    width: auto;
  }
  .header.m-wrap .logo{
    width: auto;
  }
  .culture2-wrap .culture2-ul .items .images img{
    width: 100%;
  }
  .culture2-wrap .culture2-ul .items .words{
    width: 100%;
    position: static;
    color:#666;
  }
  .footer-main .left-wrap .tx1{
    max-width: 100%;
  }
  .dj-tit-tip{
    height: 38px;
  }
  .characteristics-wrap .items:before{
    display: block!important;
  }

  .dj-news .words-box .items .h0{
    font-size: 16px;
  }
  .characteristics-wrap{
    margin-top: 20px;
  }
  .guidance-top .guidance-tit{
    font-size: 20px;
    margin-bottom: 20px;
  }
  .culture0-wrap {
    padding-top: 55px;
    padding-bottom: 20px;
  }
  .culture1-wrap {
      padding-top: 55px;
      padding-bottom: 80px;
  }
  .culture1-wrap .culture1-ul{
    margin-top: 35px;
  }
  .culture2-wrap {
      padding-top: 50px;
      padding-bottom: 60px;
  }
  .culture2-wrap .culture2-ul{
    margin-top: 40px;
  }

  .culture2-wrap .culture2-ul .items{
    width: 100%;
    margin-left:0;
    margin-bottom: 20px;
  }
  .wel-tip-box .items .itembot{
    
    bottom: 80px;
  }
  .profile-wrap .f48{
    font-size: 25px;
  }
  .profile-wrap .f36{
    font-size: 20px;
  }

  .wel-news-box .titbox {
    line-height: 40px;
    height: 40px;
    margin-bottom: 25px;
  }
  .profile0-about-wrap .left-images{
    width:100%;
  }
  .profile0-about-wrap .right-words{
    width: 100%;
    margin-top: 20px;
  }

  .wel-news-box .item {
    padding: 20px 0;
    padding-right: 25px;
  }

  .wel-news-box .item .right .h0 {
    margin-bottom: 10px;
  }

  .wel-news-box .item .left {
    width: 120px;
    padding: 0 3%;
    box-sizing: border-box;
  }

  .wel-news-box .item .right {
    width: calc(100% - 120px);
  }

  .wel-news-box .item .right .h0 {
    font-size: 18px;
  }

  .wel-news-box {
    padding-top: 25px;
    padding-bottom: 50px;
  }

  .wel-news-box .titbox .more {
    margin-left: 25px;
  }

  .wel-tip-box .items .itemtop {
    font-size: 16px;
  }
  .profile-wrap.m-wrap{
        padding-top: 25px;
    padding-bottom: 30px;
  }

  .ad-road-wrap .road-ul .word{
        width: calc(100% - 60px);
  }
  .ad-road-wrap .road-ul .words:before{
    width: 45px;
  }
  .ad-road-wrap .road-ul .word .box .p{
    font-size: 14px;
    line-height: 25px;
  }
  .ad-road-wrap .road-ul .word .box .h0{
    padding:0 20px;
  }
  .ad-road-wrap .road-ul .word .g0{
    margin-bottom: 20px;
  }
  .culture1-wrap .culture1-ul .words, .culture1-wrap .culture1-ul .images{
    width: 100%;
  }
  .culture1-wrap .culture1-ul .words{
    position: static;
  }
  .culture1-wrap .culture1-ul .items{
    margin-bottom: 20px;
  }
  .culture1-wrap .culture1-ul .words .p{
    margin-bottom: 20px;
  }
  .imgcenter{
    display: none;
  }
  .guidance-top .guidance-list .imgbot .li{
    width: 49%;
    margin-left: 2%;
    margin-top: 2%;
  }
  .guidance-top .guidance-list .imgbot .li img{
    width: 100%;
    display: block;
  }
  .guidance-top .guidance-list .imgbot .li:nth-child(2n+1){
      margin-left:0;
  }
  .garden .items .words{
    position: static;
    width: 100%;
    text-align: justify!important;
  }
  .garden .items .image{
    width: 100%;
  }
  .pc0{
    display: none;
  }
  .m0{
    display: block;
  }
  .m0 .m-ins-navbox {
    display: block!important;
  }
  .characteristics-wrap .items{
    width: 100%;
  }
  .characteristics-wrap .items img{
    width: 100%;
  }
  .characteristics-wrap .items .p{
    height:  auto;
  }
  .characteristics-wrap .items{
    border:none;
  }
  .dj1-wrap .left-words .t0{
    font-size: 24px;
  }
  .dj1-wrap .left-words .t1{
    font-size: 20px;
  }
  .dj1-wrap .left-words,.dj1-wrap .right-words{
    width: 100%;
    min-height: auto;
  }
  .dj1-wrap .right-words{
    border-left: none;
    padding:20px 0;
  }
  .dj1-wrap{
    min-height: 240px;
  }
  .dj1-wrap .right-words .pxc{
    padding-left:0;
  }
  .dj1-wrap .right-words .pxc{
    line-height: 1.8;
  }
  .dj1-wrap .right-words .pxc .pli{
    padding-left: 30px
  }
  .dj1-wrap .right-words .pxc .pli:after{
    width: 20px;
    height: 20px;    bottom: auto;
    top: 1px;
  }
  .dj-news .words-box{
    width: 100%;
  }
  .dj-news .images-box{
    width: 100%;
  }
  .dj-news .images-box img{
    width: 100%;
  }

}

@media screen and (max-width: 900px){
  .ab-honor-wrap .items .left-imaegs{
    width: 100%;
  }
  .guidance-bot{
    padding-bottom: 40px;
    margin-top: 25px;
  }
  .news-infor.news-infor-2 .wrap2 .box{
    width: 49%;
  }
  .news-infor.news-infor-2 .wrap2 .box:nth-child(3n+1){
    margin-left: 2%;
  }
  .news-infor.news-infor-2 .wrap2 .box:nth-child(2n+1){
    margin-left: 0;
  }


    .dj-news-0 .news-infor .wrap2 .box{
    width: 49%;
  }
  .dj-news-0 .news-infor .wrap2 .box:nth-child(5n+1){
    margin-left: 2%;
  }
  .dj-news-0 .news-infor .wrap2 .box:nth-child(3n+1){
    margin-left: 2%;
  }
  .dj-news-0 .news-infor .wrap2 .box:nth-child(2n+1){
    margin-left: 0;
  }



  .guidance-top{
    padding-top: 35px;
  }
  .guidance-bot .guidance0-ul .items{
    padding-top: 25px;
    padding-bottom: 25px;
  }
  .guidance-bot .tit{
    font-size: 16px;
    line-height: 50px;
    height: 50px;
  }
  .wel-con-bot .y0{
    font-size: 12px;
  }

  .wel-con-bot .y1{
    font-size: 15px;
  }
  .wel-con-bot .con-box .images{
        width: calc(100% - 160px);
  }
  .c-tit.t40{
    font-size: 20px;
  }
  .header .nav li a{
    font-size: 14px;
  }
  .footer .secbox span{
    display: inline-block;
  }
   .ab-honor-wrap .items .left-imaegs img{
    width: 100%;
   }
   .footer-main .left-wrap .tx1 .i{
    width: 100%;
   }
   .header{
    width: 100%;
    padding:20px 3%;
    box-sizing: border-box;
   }
   .header.twrap .nav li{
    padding:0 5px;
    line-height: 28px;
   }
   .header.fixed{
    height: auto;
   }
  .ab-honor-wrap .items .right-words{
    width: 100%;
    position: static;
  }
  .ab-honor-wrap{
    margin-top: 35px;
  }
  .footer .secbox{
    height: auto;
    line-height: 2;
  }
  .footer .thibox .right {
    display: block;
    width: 100%;
  }
  .footer-main .wxbox{
    width: 30%!important;
    float:left;
    margin-left:0!important;
    margin-right: 5%;
  }
  .footer .secbox .right a{
    float:none;
  }
  .footer-main .left-wrap{
    width: 100%;
  }
  .footer-main .right{
    width: 100%;
  }

  .ad-road-wrap .road-ul:after{
    width: 16px;
    height: 16px;
    left: -8px;
    right:auto;
  }
  .ad-road-wrap .road-ul:before,.ad-road-wrap .road-ul .items:before{
    right:auto;
    left: -1px;
  }
  .ad-road-wrap .road-ul .words.fl .g0{
    text-align: left;
  }
  .ad-road-wrap .road-ul .items:nth-child(n+2) .words{
    margin-top:0;

  }
  .ad-road-wrap .road-ul .words.fl:before{
    right: auto;
    left:0;
  }
  .ad-road-wrap .road-ul .words{
  width: 100%;
  min-height: auto;

  }
  .ad-road-wrap .road-ul .word{
    float:right;
  }
  .ad-road-wrap .road-ul .words img{
    width: 100%;
  }
  .ad-road-wrap .road-tit{
    overflow: hidden;
  }
  .ad-road-wrap .road-tit .span{
      font-size: 12px;
  }

}

@media screen and (max-width: 800px){
	.course-wrap .course-list .butn{
		display: none!important;
	}
	.course-wrap .course-list li .g0{
		margin-bottom: 5px;
	}
	.course-wrap .course-list li{
		top:0;
	}
	.course-wrap .course-list li:hover .g0{
		font-size: 20px;
	}
	.course-wrap .course-list li:before{
		height: 100%;
	}
	.course-wrap .course-list li{
		padding:0;
		font-size: 14px;
		padding-left: 18px;
		padding-bottom: 20px;
		width: 100%;
		line-height: 1.6;
	}
	.course-wrap .course-list li.odd:before{
		top: 0;
	}
	.course-wrap .course-list{
		margin-top:0;
	}
	.course-wrap .course-list li.odd{
		top:0;
	}
	.course-wrap .course-list .ovh{
		width: 95%;
	}
	.course-wrap{
		padding-top:0;
	}
	.course-wrap .course-list{
		background:transparent;
	}
}
@media screen and (max-width: 780px){
  .h75{
    height: 38px;
  }
  .characteristics-wrap .items:before{
    top:10px;
  }
  .wrap.m-wrap.header{
    width: 100%;
    box-sizing: border-box;
  }
  .kwords{
    font-size: 14px;
    line-height: 25px;
  }
  .characteristics-wrap .items .p{
    font-size: 14px;
    line-height: 24px;
  }
  .characteristics-wrap .items .t1{
    margin-top: 8px;
    margin-bottom: 14px;
    font-size: 14px;
  }
  .characteristics-wrap .items{
    padding:10px;
  }
  .garden .items .words{
    padding:5%;
  }
  .t60{
    font-size: 20px;
  }
  .ad-road-wrap .road-tit{
    width: 100%;
  }
  .culture2-wrap .culture2-ul .items .words .t0{
    font-size: 18px;
  }
  .guidance-top .guidance-list{
    padding:20px 25px;
  }
}
@media screen and (max-width: 768px) {
	.ab-honor-wrap .items .right-words .ul .li .li-bot{
		position: static;
		width: 100%;
		display: block;
		background:transparent;
		padding: 10px 20px;
		font-size: 16px;
		color:#888;
		display: none!important;

	}
	.ab-honor-wrap .items .right-words .ul .li .li-bot:after{
		display: none;
	}
  .wel-news-box .titbox .t40 {
    font-size: 20px;
  }
  .news-infor.news-infor-2 .wrap2 .box{
    width: 100%;
    margin-left:0!important;
  }
    .dj-news-0 .news-infor .wrap2 .box{
    width: 100%;
    margin-left: 0!important;
  }
  .dj-news .words-box .items .time{
    font-size: 13px;
    margin-bottom: 10px;
  }
  .dj-news .words-box .items .p{
    font-size: 14px;
    line-height: 24px;
    height: 48px;
  }
  .dj-news-0 .news-infor .wrap2 .box a{
    padding:20px;
  }
 .dj-news-0  .news-infor .wrap2 .box .f18{
    font-size: 16px;
  }
  .dj-news ,.dj-news-0{
      padding-top: 30px;
      padding-bottom: 35px;
  }

  .garden{
    padding-top: 30px;
  }
  .culture1-wrap .culture1-ul{
    margin-top: 20px;
  }
  .culture0-wrap .culture0-ul{
    margin-top: 30px;
  }
  .culture0-wrap{
    padding-top: 35px;
  }
  .culture0-wrap .words{
    margin-top: 20px;
  }
  .culture0-wrap .words .h0{
    font-size: 16px;
  }
  .culture0-wrap .words .h1{
    font-size: 13px;
    margin-top: 5px;
  }
  .culture0-wrap .images{
    width: 70px;
    height: 70px;
  }
  .culture0-wrap .culture0-line{
    height: 35px;
  }
  .culture2-wrap .culture2-ul .items .words{
    padding:15px 20px;
  }
  .footer-main.m-wrap{
    padding:15px 10px;
    width: 100%;
    box-sizing: border-box;
  }
  .garden-swiper0 .swiper-pagination .swiper-pagination-bullet{
    width: 30px;
  }
  .garden .items .words .p{
    line-height: 1.6;
  }
  .t60{
    font-size: 16px;
  }
  .ad-road-wrap .road-tit {
    margin-top: 25px;
    margin-bottom: 20px;
  }
  .ad-road-wrap .road-ul .word .box .h0{
    height: auto;
    line-height: 1.5;
    padding:15px ;
    box-sizing: border-box;
    display: block;
    overflow: visible;
    font-size: 14px;
  }

  .ad-road-wrap .road-ul .word {
    width: calc(100% - 25px);
  }
  .ad-road-wrap .road-ul .words:before{
    width: 20px;
  }
  .ad-road-wrap .road-ul .word .box .img{
    padding:15px;
  }
  .ab-honor-wrap .items .right-words .ul .li{
    width: 100%;
    height: auto;
    line-height: 2;
  }
  .ab-honor-wrap .items .right-words .ul .ul-top{
    font-size: 16px;
  }
  .wel-tip-box .items .itembot{
    bottom: 75px;
  }
  .profile-wrap {
      padding-top: 35px;
      padding-bottom: 45px;
  }
  .down-box .items{
    width: 100%;
    margin-left:0!important;
    margin-bottom: 15px;
  }
  .down-box .items .words .more{
    margin-top: 20px;
  }
  .down-box .items .words .t0{
    font-size: 16px;
  }
  .down-box .items .words .t1{
    font-size: 14px;
    line-height: 20px;
    height: 60px;
  }

  .profile-wrap .f48{
    font-size: 20px;
  }
  .profile-wrap .f36{
    font-size: 18px;
  }
  .wel-con-bot {
    height: 100px;
    line-height: 100px;
  }

  .wel-news-box .titbox .more {
    font-size: 14px;
    margin-left: 18px;
  }

  .wel-news-box .item .left .num {
    font-size: 30px;
  }

  .wel-tip-box .items {
    font-weight: normal;
  }

  .wel-news-box .item {
    padding: 18px 0;
    padding-right: 1%;
    position: relative;
  }

  .wel-tip-box .items .itemtop br {
    display: block;
  }

  .wel-tip {
    position: relative;
  }

  .wel-tip-box .items .itemtop img {
    display: block;
    margin: 0 auto;
    width: 20px;
    height: 20px;
  }

  .wel-tip-box .items .itemtop .span {
    display: block;
    line-height: 16px;
    font-size: 14px;
    height: 32px;
    margin-top: 5px;
  }

  .wel-tip-box .items {
    padding: 10px 0;
    box-sizing: border-box;
    position: static;
  }
  .culture2-wrap{
    padding-top: 35px;
    padding-bottom: 30px;
  }
  .culture2-wrap .culture2-ul .items{
    margin-bottom: 15px;
  }
  .culture2-wrap .culture2-ul{
    margin-top: 25px;
  }
  .culture1-wrap{
    padding:35px 0;
  }
  .culture1-wrap .culture1-ul .words .wul .i{
    margin:0;
    width: 50%;
  }
  .culture1-wrap .culture1-ul .words{
    padding:20px;
  }
  .culture1-wrap .culture1-ul .words .p{
    line-height: 1.8;
  }
  .culture1-wrap .culture1-ul .words .t0{
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 15px;
    height:  auto;
  }
  .dj-wrap{
    padding-top:30px;
    box-sizing: border-box;
  }
  .dj1-wrap .left-words .t0{
    font-size: 20px;
  }
  .dj1-wrap .left-words .t1{
    font-size: 18px;
  }
  .ab-honor-wrap{
    margin-top: 20px;
  }
}
@media screen and (max-width: 500px) {
  .wel-news-box .item .left .time {
    font-size: 14px;
  }

  .culture2-wrap .culture2-ul .items .words .t0{
    font-size: 15px;
    margin-bottom: 6px;
  }
  .culture2-wrap .culture2-ul .items .words .t1{
    line-height: 20px;
    font-size: 12px;
  }
  .wel-con-bot .y1{
    font-size: 13px;
    line-height: 1.2;
  }

  .wel-con-bot {
    height: 80px;
    line-height: 80px;
  }
  .wel-con-bot .con-box {
    height: 100%;
  }
  .wel-con-bot img {
    height: 60%;
  }
  .wel-con-bot .images {
    height: 100%;
  }

  .wel-news-box .item .left .num {
    font-size: 22px;
  }

  .wel-news-box .item .left {
    width: 75px;
  }

  .wel-con-bot .words {
    font-size: 12px;
  }

  .wel-news-box .item .right {
    width: calc(100% - 80px);
  }

  .wel-news-box .item .right .h0 {
    font-size: 16px;
    line-height: 20px;
    height: 20px;
  }

  .wel-news-box .item .right .p {
    font-size: 13px;
    line-height: 20px;
    height: 40px;
  }

  .wel-news-box .item {
    padding: 13px 0;
    margin-bottom: 0;
    padding-right: 1%;
  }

  .wel-news-box {
    padding-bottom: 45px;
  }

  .wel-news-box .swiper-pagination {
    font-size: 14px;
  }

  .wel-tip-box .items .itemtop .span {
    font-size: 12px;
  }

  .wel-news-box .titbox {
    line-height: 30px;
    margin-bottom: 15px;
    height: 30px;
  }

  .wel-news-box .titbox .t40 {
    font-size: 18px;
  }
}

@media screen and (max-width: 375px) {
  .garden .items .words .p{
    font-size: 13px;
  }
  .c-tit.t40{
    font-size: 18px;
  }
  .culture1-wrap .culture1-ul .words .t0{
    font-size: 16px;
  }
    .dj1-wrap .left-words .t0{
      font-size: 18px;
    }
    .dj1-wrap .left-words .t1{
      font-size: 15px;
      margin-top: 6px;
    }
    .profile-wrap.m-wrap .f48{
      font-size: 16px;
    }

}
.garden .items .image .swiper-pagination .swiper-pagination-bullet{
	opacity: 1;
	width: 30px;
	background:#fff;
	height: 4px;
	border-radius: 0;
}
.garden .items .image .swiper-pagination .swiper-pagination-bullet-active{
	opacity: 1;
	background:#f34b1b;
}